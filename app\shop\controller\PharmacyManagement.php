<?php
namespace app\shop\controller;

use app\model\pharmacy\PharmacyStock as PharmacyStockModel;
use app\model\pharmacy\PharmacyStockLog as PharmacyStockLogModel;
use app\model\pharmacy\PharmacyProductionLog as PharmacyProductionLogModel;
use app\model\pharmacy\PharmacySplitOrder as PharmacySplitOrderModel;
use app\model\pharmacy\PharmacyStockinOrder as PharmacyStockinOrderModel;
use app\model\order\OrderCommon as OrderCommonModel; // 新增

/**
 * 药房管理控制器
 * Class PharmacyManagement
 * @package app\shop\controller
 */
class PharmacyManagement extends BaseShop
{
    /**
     * 药房管理主页面
     */
    public function index()
    {
        $this->assign('page_title', '药房库存管理');
        return $this->fetch('pharmacy/index');
    }

    /**
     * 拆零操作独立页面
     */
    public function split()
    {
        $this->assign('page_title', '拆零操作');
        return $this->fetch('pharmacy/split');
    }

    /**
     * 生产计划独立页面
     */
    public function production()
    {
        $this->assign('page_title', '生产计划');
        return $this->fetch('pharmacy/production');
    }

    /**
     * 入库操作独立页面
     */
    public function stockin()
    {
        $this->assign('page_title', '入库操作');
        return $this->fetch('pharmacy/stockin');
    }

    /**
     * 批量拆零操作
     */
    public function batchSplitOperation()
    {
        if (request()->isAjax()) {
            $items = input('items', []);

            if (empty($items)) {
                return json(error(-1, '请添加拆零项目'));
            }

            $split_order_model = new PharmacySplitOrderModel();
            $operator_info = [
                'site_id' => $this->site_id,
                'uid' => $this->uid,
                'user_name' => $this->user_info['user_name'] ?? '系统操作员'
            ];

            $result = $split_order_model->createSplitOrder($items, $operator_info);
            return json($result);
        }
    }

    /**
     * 套餐生产操作（增强版 - 支持拆零步骤）
     */
    public function packageProduction()
    {
        if (request()->isAjax()) {
            $template_id = input('template_id', 0);
            $quantity = input('quantity', 0);
            $split_verified = input('split_verified', 0); // 是否已完成拆零验证

            if ($template_id <= 0) {
                return json(error(-1, '请选择处方模板'));
            }

            if ($quantity <= 0) {
                return json(error(-1, '请输入有效的生产数量'));
            }

            // 将套数转换为餐数：1套 = 30餐
            $meals_quantity = $quantity * 30;

            $pharmacy_stock_model = new PharmacyStockModel();
            $operator_info = [
                'site_id' => $this->site_id,
                'uid' => $this->uid,
                'user_name' => $this->user_info['user_name'] ?? '系统操作员'
            ];

            // 如果未完成拆零验证，返回拆零页面数据
            if (!$split_verified) {
                $split_data = $pharmacy_stock_model->getSplitRequirements($template_id, $meals_quantity);
                if ($split_data['code'] == 0) {
                    return json([
                        'code' => 2, // 特殊状态码，表示需要拆零验证
                        'message' => '需要完成拆零验证',
                        'data' => $split_data['data']
                    ]);
                } else {
                    return json($split_data);
                }
            }

            // 已完成拆零验证，执行正式生产
            $result = $pharmacy_stock_model->producePackage($template_id, $meals_quantity, $operator_info);
            return json($result);
        }
    }

    /**
     * 获取拆零验证数据
     */
    public function getSplitRequirements()
    {
        if (request()->isAjax()) {
            $template_id = input('template_id', 0);
            $quantity = input('quantity', 0);

            if ($template_id <= 0) {
                return json(error(-1, '请选择处方模板'));
            }

            if ($quantity <= 0) {
                return json(error(-1, '请输入有效的生产数量'));
            }

            // 将套数转换为餐数：1套 = 30餐
            $meals_quantity = $quantity * 30;

            $pharmacy_stock_model = new PharmacyStockModel();
            $result = $pharmacy_stock_model->getSplitRequirements($template_id, $meals_quantity);
            return json($result);
        }
    }

    /**
     * 验证拆零扫码结果
     */
    public function verifySplitScan()
    {
        if (request()->isAjax()) {
            $sku_id = input('sku_id', 0);
            $scan_code = input('scan_code', '');
            $bottles_count = input('bottles_count', 1);

            if ($sku_id <= 0) {
                return json(error(-1, '无效的商品ID'));
            }

            if (empty($scan_code)) {
                return json(error(-1, '请扫描商品条码'));
            }

            $pharmacy_stock_model = new PharmacyStockModel();
            $result = $pharmacy_stock_model->verifySplitScan($sku_id, $scan_code, $bottles_count);
            return json($result);
        }
    }

    /**
     * 根据扫码查找匹配的SKU
     */
    public function findSkuByScanCode()
    {
        if (request()->isAjax()) {
            $scan_code = input('scan_code', '');
            $sku_list = input('sku_list', []); // 候选SKU列表

            if (empty($scan_code)) {
                return json(error(-1, '扫码内容为空'));
            }

            $pharmacy_stock_model = new PharmacyStockModel();
            $result = $pharmacy_stock_model->findSkuByScanCode($scan_code, $sku_list);
            return json($result);
        }
    }



    /**
     * 确认最终生产套数
     */
    public function confirmFinalQuantity()
    {
        if (request()->isAjax()) {
            $production_id = input('production_id', 0);
            $final_quantity = input('final_quantity', 0);

            if ($production_id <= 0) {
                return json(error(-1, '无效的生产记录ID'));
            }

            if ($final_quantity < 0) {
                return json(error(-1, '最终套数不能为负数'));
            }

            $production_log_model = new \app\model\pharmacy\PharmacyProductionLog();
            $operator_info = [
                'site_id' => $this->site_id,
                'uid' => $this->uid,
                'user_name' => $this->user_info['user_name'] ?? '系统操作员'
            ];

            $result = $production_log_model->confirmFinalQuantity($production_id, $final_quantity, $operator_info);
            return json($result);
        }
    }

    /**
     * 批量入库操作
     */
    public function batchStockInOperation()
    {
        if (request()->isAjax()) {
            $items = input('items', []);

            if (empty($items)) {
                return json(error(-1, '请添加入库项目'));
            }

            $stockin_order_model = new PharmacyStockinOrderModel();
            $operator_info = [
                'site_id' => $this->site_id,
                'uid' => $this->uid,
                'user_name' => $this->user_info['user_name'] ?? '系统操作员'
            ];

            $result = $stockin_order_model->createStockinOrder($items, $operator_info);
            return json($result);
        }
    }

    /**
     * 获取处方模板列表
     */
    public function getTemplateList()
    {
        if (request()->isAjax()) {
            try {
                $condition = [
                    ['site_id', '=', $this->site_id],
                    ['uid', '=', 1],// 系统处方模板
                    ['guding', '=', 1] // 固定处方模板
                ];

                $list = model('order_attr_class')->getList($condition,
                    'class_id,class_name,price,goods_name,goods_num,sku_ids',
                    'sort asc, class_id desc');

                // 确保返回的是数组
                if (!is_array($list)) {
                    $list = [];
                }

                return json([
                    'code' => 0,  // 与其他接口保持一致
                    'message' => '获取成功',
                    'data' => $list
                ]);
            } catch (\Exception $e) {
                return json([
                    'code' => -1,
                    'message' => '获取模板列表失败：' . $e->getMessage(),
                    'data' => []
                ]);
            }
        }

        return json([
            'code' => -1,
            'message' => '非AJAX请求',
            'data' => []
        ]);
    }

    /**
     * 获取实时库存
     */
    public function getRealTimeStock()
    {
        if (request()->isAjax()) {
            $sku_no = input('sku_no', '');

            if (empty($sku_no)) {
                return json(error(-1, '请输入SKU编码'));
            }

            // 根据sku_no查找sku_id
            $sku_info = model('goods_sku')->getInfo([['sku_no', '=', $sku_no]], 'sku_id');
            if (empty($sku_info)) {
                return json(error(-1, '未找到对应的SKU'));
            }

            $pharmacy_stock_model = new PharmacyStockModel();
            $result = $pharmacy_stock_model->getRealTimeStock($sku_info['sku_id']);
            return json($result);
        }
    }

    /**
     * 获取操作记录
     */
    public function getOperationLog()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', 20);
            $operation_type = input('operation_type', '');
            $sku_name = input('sku_name', '');

            $condition = [['site_id', '=', $this->site_id]];

            if (!empty($operation_type)) {
                $condition[] = ['operation_type', '=', $operation_type];
            }

            if (!empty($sku_name)) {
                $condition[] = ['sku_name', 'like', '%' . $sku_name . '%'];
            }

            $log_model = new PharmacyStockLogModel();
            $result = $log_model->getLogList($condition, $page, $page_size);

            // 确保返回Layui表格期望的格式
            if ($result['code'] == 0) {  // BaseModel success方法返回code: 0表示成功
                return json([
                    'code' => 0, // Layui表格期望code为0表示成功
                    'msg' => 'success',
                    'count' => $result['data']['count'] ?? 0,
                    'data' => $result['data']['list'] ?? []
                ]);
            } else {
                return json([
                    'code' => 1, // 错误
                    'msg' => $result['message'] ?? '获取数据失败',
                    'count' => 0,
                    'data' => []
                ]);
            }
        }
    }

    /**
     * 获取生产记录
     */
    public function getProductionLog()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', 20);
            $template_name = input('template_name', '');
            $start_date = input('start_date', '');
            $end_date = input('end_date', '');

            $condition = [['site_id', '=', $this->site_id]];

            if (!empty($template_name)) {
                $condition[] = ['template_name', 'like', '%' . $template_name . '%'];
            }

            // 添加日期范围筛选
            if (!empty($start_date)) {
                $condition[] = ['create_time', '>=', $start_date . ' 00:00:00'];
            }

            if (!empty($end_date)) {
                $condition[] = ['create_time', '<=', $end_date . ' 23:59:59'];
            }

            $production_model = new PharmacyProductionLogModel();
            $result = $production_model->getProductionList($condition, $page, $page_size);

            // 确保返回Layui表格期望的格式
            if ($result['code'] == 0) {
                return json([
                    'code' => 0,
                    'msg' => 'success',
                    'count' => $result['data']['count'] ?? 0,
                    'data' => $result['data']['list'] ?? []
                ]);
            } else {
                return json([
                    'code' => 1,
                    'msg' => $result['message'] ?? '获取数据失败',
                    'count' => 0,
                    'data' => []
                ]);
            }
        }
    }

    /**
     * 获取生产记录详情
     */
    public function getProductionDetail()
    {
        if (request()->isAjax()) {
            $production_id = input('production_id', 0);

            if ($production_id <= 0) {
                return json(error(-1, '无效的生产记录ID'));
            }

            $production_model = new PharmacyProductionLogModel();
            $result = $production_model->getProductionDetail($production_id);
            return json($result);
        }
    }

    /**
     * 获取今日生产记录
     */
    public function getTodayProductionRecords()
    {
        if (request()->isAjax()) {
            $today = date('Y-m-d');
            $condition = [
                ['site_id', '=', $this->site_id],
                ['create_time', '>=', $today . ' 00:00:00'],
                ['create_time', '<=', $today . ' 23:59:59'],
                ['status', '=', 1]  // 只显示成功的记录
            ];

            $production_model = new \app\model\pharmacy\PharmacyProductionLog();
            $result = $production_model->getTodayRecords($condition);
            return json($result);
        }
    }

    /**
     * 计算生产所需原料和成本
     */
    public function calculateProduction()
    {
        if (request()->isAjax()) {
            $template_id = input('template_id', 0);
            $quantity = input('quantity', 1);

            if (empty($template_id) || $quantity <= 0) {
                return json(['code' => 1, 'message' => '参数错误']);
            }

            // 将套数转换为餐数：1套 = 30餐
            $meals_quantity = $quantity * 30;

            try {
                // 获取模板信息
                $template_info = model('order_attr_class')->getInfo([['class_id', '=', $template_id]],
                    'class_id,class_name,goods_name,goods_num,sku_ids');

                if (!$template_info) {
                    return json(['code' => 1, 'message' => '模板不存在']);
                }

                // 解析商品名称和数量
                $goods_names = json_decode($template_info['goods_name'], true);
                $goods_nums = json_decode($template_info['goods_num'], true);

                // 检查JSON解析是否成功
                if ($goods_names === null || $goods_nums === null) {
                    return json(['code' => 1, 'message' => '模板数据格式错误']);
                }

                // 检查模板内容是否为空
                if (empty($goods_names) || empty($goods_nums)) {
                    return json(['code' => 1, 'message' => '模板内容为空，请先配置模板商品']);
                }

                $calculation_result = [];
                $total_cost = 0;

                // 计算每个商品的消耗
                for ($i = 0; $i < count($goods_names); $i++) {
                    if (isset($goods_names[$i]) && isset($goods_nums[$i])) {
                        $goods_name_item = $goods_names[$i];
                        $need_quantity = intval($goods_nums[$i]) * $meals_quantity;

                        // 解析 goods_name 格式：时间段_sku_id
                        $goods_parts = explode('_', $goods_name_item);
                        if (count($goods_parts) >= 2) {
                            $sku_id = intval($goods_parts[1]); // 获取 sku_id

                            // 根据 sku_id 查询商品库存信息
                            $sku_info = model('goods_sku')->getInfo([
                                ['site_id', '=', $this->site_id],
                                ['sku_id', '=', $sku_id]
                            ], 'sku_id,sku_name,whole_stock,loose_stock,daizhuang,cost_price,goods_id');

                            if ($sku_info) {
                                // 只考虑整瓶库存的可用性（业务规则：生产只能使用整瓶，不能使用散药）
                                $available_stock = $sku_info['whole_stock'] * $sku_info['daizhuang'];

                                // 计算所需整瓶数量
                                $bottles_needed = ceil($need_quantity / $sku_info['daizhuang']);
                                $bottles_available = $sku_info['whole_stock'];

                                $cost = $need_quantity * floatval($sku_info['cost_price'] ?? 0);
                                $total_cost += $cost;

                                $calculation_result[] = [
                                    'sku_name' => $sku_info['sku_name'], // 使用实际的商品名称
                                    'original_name' => $goods_name_item, // 原始的格式
                                    'sku_id' => $sku_id,
                                    'need_quantity' => $need_quantity,
                                    'available_stock' => $available_stock, // 只显示整瓶库存的颗粒数
                                    'whole_stock' => $sku_info['whole_stock'],
                                    'loose_stock' => $sku_info['loose_stock'],
                                    'daizhuang' => $sku_info['daizhuang'],
                                    'bottles_needed' => $bottles_needed, // 新增：所需整瓶数
                                    'bottles_available' => $bottles_available, // 新增：可用整瓶数
                                    'cost' => $cost,
                                    'sufficient' => $bottles_available >= $bottles_needed // 修正：基于整瓶数量判断
                                ];
                            } else {
                                $calculation_result[] = [
                                    'sku_name' => '商品不存在 (SKU ID: ' . $sku_id . ')',
                                    'original_name' => $goods_name_item,
                                    'sku_id' => $sku_id,
                                    'need_quantity' => $need_quantity,
                                    'available_stock' => 0,
                                    'whole_stock' => 0,
                                    'loose_stock' => 0,
                                    'daizhuang' => 0,
                                    'cost' => 0,
                                    'sufficient' => false
                                ];
                            }
                        } else {
                            // 格式不正确的情况
                            $calculation_result[] = [
                                'sku_name' => '格式错误: ' . $goods_name_item,
                                'original_name' => $goods_name_item,
                                'sku_id' => 0,
                                'need_quantity' => $need_quantity,
                                'available_stock' => 0,
                                'whole_stock' => 0,
                                'loose_stock' => 0,
                                'daizhuang' => 0,
                                'cost' => 0,
                                'sufficient' => false
                            ];
                        }
                    }
                }

                return json([
                    'code' => 0,
                    'message' => '计算成功',
                    'data' => [
                        'template_name' => $template_info['class_name'],
                        'production_quantity' => $quantity,
                        'total_cost' => $total_cost,
                        'ingredients' => $calculation_result,
                        'can_produce' => !in_array(false, array_column($calculation_result, 'sufficient'))
                    ]
                ]);

            } catch (\Exception $e) {
                return json(['code' => 1, 'message' => '计算失败：' . $e->getMessage()]);
            }
        }
    }

    /**
     * 获取拆零操作详情
     */
    public function getSplitDetail()
    {
        if (request()->isAjax()) {
            $split_id = input('split_id', 0);

            if ($split_id <= 0) {
                return json([
                    'code' => -1,
                    'message' => '无效的拆零记录ID',
                    'data' => []
                ]);
            }

            $split_model = new PharmacySplitOrderModel();
            $result = $split_model->getSplitOrderDetail($split_id);
            return json($result);
        }
    }

    /**
     * 获取入库操作详情
     */
    public function getStockinDetail()
    {
        if (request()->isAjax()) {
            $stockin_id = input('stockin_id', 0);

            if ($stockin_id <= 0) {
                return json([
                    'code' => -1,
                    'message' => '无效的入库记录ID',
                    'data' => []
                ]);
            }

            $stockin_model = new PharmacyStockinOrderModel();
            $result = $stockin_model->getStockinOrderDetail($stockin_id);
            return json($result);
        }
    }

    /**
     * 获取操作统计
     */
    public function getOperationStats()
    {
        if (request()->isAjax()) {
            $condition = [['site_id', '=', $this->site_id]];
            
            $log_model = new PharmacyStockLogModel();
            $production_model = new PharmacyProductionLogModel();
            
            $operation_stats = $log_model->getOperationStats($condition);
            $production_stats = $production_model->getProductionStats($condition);
            
            return json([
                'code' => 0,  // 与其他接口保持一致
                'message' => '获取成功',
                'data' => [
                    'operation_stats' => $operation_stats['data'],
                    'production_stats' => $production_stats['data']
                ]
            ]);
        }
    }

    /**
     * 获取SKU操作历史
     */
    public function getSkuHistory()
    {
        if (request()->isAjax()) {
            $sku_no = input('sku_no', '');
            $limit = input('limit', 10);

            if (empty($sku_no)) {
                return json(error(-1, '请输入SKU编码'));
            }

            // 根据sku_no查找sku_id
            $sku_info = model('goods_sku')->getInfo([['sku_no', '=', $sku_no]], 'sku_id');
            if (empty($sku_info)) {
                return json(error(-1, '未找到对应的SKU'));
            }

            $log_model = new PharmacyStockLogModel();
            $result = $log_model->getSkuHistory($sku_info['sku_id'], $limit);
            return json($result);
        }
    }

    /**
     * 导出操作日志
     */
    public function exportOperationLog()
    {
        if (request()->isAjax()) {
            $operation_type = input('operation_type', '');
            $sku_name = input('sku_name', '');
            $start_date = input('start_date', '');
            $end_date = input('end_date', '');

            $condition = [['site_id', '=', $this->site_id]];

            if (!empty($operation_type)) {
                $condition[] = ['operation_type', '=', $operation_type];
            }

            if (!empty($sku_name)) {
                $condition[] = ['sku_name', 'like', '%' . $sku_name . '%'];
            }

            if (!empty($start_date)) {
                $condition[] = ['create_time', '>=', $start_date . ' 00:00:00'];
            }

            if (!empty($end_date)) {
                $condition[] = ['create_time', '<=', $end_date . ' 23:59:59'];
            }

            $log_model = new PharmacyStockLogModel();
            $result = $log_model->exportLogs($condition, 'csv');
            return json($result);
        }
    }

    /**
     * 导出生产记录
     */
    public function exportProductionLog()
    {
        if (request()->isAjax()) {
            $template_name = input('template_name', '');
            $start_date = input('start_date', '');
            $end_date = input('end_date', '');

            $condition = [['site_id', '=', $this->site_id]];

            if (!empty($template_name)) {
                $condition[] = ['template_name', 'like', '%' . $template_name . '%'];
            }

            if (!empty($start_date)) {
                $condition[] = ['create_time', '>=', $start_date . ' 00:00:00'];
            }

            if (!empty($end_date)) {
                $condition[] = ['create_time', '<=', $end_date . ' 23:59:59'];
            }

            $production_model = new PharmacyProductionLogModel();
            $result = $production_model->exportProductions($condition, 'csv');
            return json($result);
        }
    }

    /**
     * 获取拆零单列表
     */
    public function getSplitOrderList()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', 20);
            $start_date = input('start_date', '');
            $end_date = input('end_date', '');
            $product_name = input('product_name', '');

            $condition = [['site_id', '=', $this->site_id]];

            // 添加日期范围筛选
            if (!empty($start_date)) {
                $condition[] = ['create_time', '>=', $start_date . ' 00:00:00'];
            }

            if (!empty($end_date)) {
                $condition[] = ['create_time', '<=', $end_date . ' 23:59:59'];
            }

            $split_order_model = new PharmacySplitOrderModel();
            $result = $split_order_model->getSplitOrderList($condition, $page, $page_size, $product_name);

            // 确保返回Layui表格期望的格式
            if ($result['code'] == 0) {  // BaseModel success方法返回code: 0表示成功
                return json([
                    'code' => 0, // Layui表格期望code为0表示成功
                    'msg' => 'success',
                    'count' => $result['data']['count'] ?? 0,
                    'data' => $result['data']['list'] ?? []
                ]);
            } else {
                return json([
                    'code' => 1, // 错误
                    'msg' => $result['message'] ?? '获取数据失败',
                    'count' => 0,
                    'data' => []
                ]);
            }
        }
    }

    /**
     * 获取拆零单详情
     */
    public function getSplitOrderDetail()
    {
        if (request()->isAjax()) {
            $order_id = input('order_id', 0);

            if ($order_id <= 0) {
                return json(error(-1, '无效的拆零单ID'));
            }

            $split_order_model = new PharmacySplitOrderModel();
            $result = $split_order_model->getSplitOrderDetail($order_id);
            return json($result);
        }
    }

    /**
     * 获取入库单列表
     */
    public function getStockinOrderList()
    {
        if (request()->isAjax()) {
            $page = input('page', 1);
            $page_size = input('page_size', 20);
            $start_date = input('start_date', '');
            $end_date = input('end_date', '');
            $product_name = input('product_name', '');

            $condition = [['site_id', '=', $this->site_id]];

            // 添加日期范围筛选
            if (!empty($start_date)) {
                $condition[] = ['create_time', '>=', $start_date . ' 00:00:00'];
            }

            if (!empty($end_date)) {
                $condition[] = ['create_time', '<=', $end_date . ' 23:59:59'];
            }

            $stockin_order_model = new PharmacyStockinOrderModel();
            $result = $stockin_order_model->getStockinOrderList($condition, $page, $page_size, $product_name);

            // 确保返回Layui表格期望的格式
            if ($result['code'] == 0) {  // BaseModel success方法返回code: 0表示成功
                return json([
                    'code' => 0, // Layui表格期望code为0表示成功
                    'msg' => 'success',
                    'count' => $result['data']['count'] ?? 0,
                    'data' => $result['data']['list'] ?? []
                ]);
            } else {
                return json([
                    'code' => 1, // 错误
                    'msg' => $result['message'] ?? '获取数据失败',
                    'count' => 0,
                    'data' => []
                ]);
            }
        }
    }

    /**
     * 获取入库单详情
     */
    public function getStockinOrderDetail()
    {
        if (request()->isAjax()) {
            $order_id = input('order_id', 0);

            if ($order_id <= 0) {
                return json(error(-1, '无效的入库单ID'));
            }

            $stockin_order_model = new PharmacyStockinOrderModel();
            $result = $stockin_order_model->getStockinOrderDetail($order_id);
            return json($result);
        }
    }

    /**
     * 获取生产计划预览
     */
    public function getProductionPlan()
    {
        if (request()->isAjax()) {
            $template_id = input('template_id', 0);
            $quantity = input('quantity', 0);

            if ($template_id <= 0) {
                return json(error(-1, '请选择处方模板'));
            }

            if ($quantity <= 0) {
                return json(error(-1, '请输入有效的生产数量'));
            }

            // 获取处方模板信息
            $template_info = model('order_attr_class')->getInfo([['class_id', '=', $template_id]],
                'class_id,class_name,sku_ids,goods_name,goods_num');

            if (empty($template_info)) {
                return json(error(-1, '处方模板不存在'));
            }

            $sku_ids = array_filter(explode(',', $template_info['sku_ids']));
            $goods_names = json_decode($template_info['goods_name'], true) ?: [];
            $goods_nums = json_decode($template_info['goods_num'], true) ?: [];

            // 数据完整性检查
            if (empty($sku_ids)) {
                return json([
                    'code' => -1,
                    'message' => '处方模板SKU数据为空',
                    'data' => []
                ]);
            }

            if (count($sku_ids) !== count($goods_nums)) {
                return json([
                    'code' => -1,
                    'message' => '处方模板数据不完整：SKU数量与商品数量不匹配',
                    'data' => []
                ]);
            }

            $required_materials = [];
            $produced_loose = [];

            foreach ($sku_ids as $index => $sku_id) {
                // 确保SKU ID有效
                $sku_id = intval(trim($sku_id));
                if ($sku_id <= 0) {
                    continue;
                }

                // 检查数组边界
                if (!isset($goods_nums[$index])) {
                    continue;
                }

                $sku_info = model('goods_sku')->getInfo([['sku_id', '=', $sku_id]],
                    'sku_id,sku_name,whole_stock,loose_stock,daizhuang');

                if (!empty($sku_info) && $sku_info['daizhuang'] > 0) {
                    $needed_grains = floatval($goods_nums[$index]) * $quantity;
                    $bottles_needed = ceil($needed_grains / $sku_info['daizhuang']);
                    $remaining_grains = ($bottles_needed * $sku_info['daizhuang']) - $needed_grains;

                    $required_materials[] = [
                        'sku_name' => $sku_info['sku_name'],
                        'bottles_needed' => $bottles_needed,
                        'current_stock' => $sku_info['whole_stock'],
                        'sufficient' => $sku_info['whole_stock'] >= $bottles_needed
                    ];

                    if ($remaining_grains > 0) {
                        $produced_loose[] = [
                            'sku_name' => $sku_info['sku_name'],
                            'loose_grains' => $remaining_grains
                        ];
                    }
                }
            }

            return json([
                'code' => 0,  // 与其他接口保持一致
                'message' => '获取成功',
                'data' => [
                    'template_name' => $template_info['class_name'],
                    'production_quantity' => $quantity,
                    'required_materials' => $required_materials,
                    'produced_loose' => $produced_loose
                ]
            ]);
        }
    }

    /**
     * 获取今日生产任务订单（待发货 + extend_id>0）
     */
    public function getTodayProductionOrders()
    {
        if (request()->isAjax()) {
            $count = (int) input('count', 10);
            if ($count <= 0) { $count = 10; }

            $order_common_model = new OrderCommonModel();

            $condition = [
                ['site_id', '=', $this->site_id],
                ['is_delete', '=', 0],
                ['order_status', '=', OrderCommonModel::ORDER_PAY], // 待发货
                ['extend_id', 'not in', ['0', '']], // 外部订单号存在
            ];

            // TODO: 过滤“未标记当天生产序号”的订单（需提供标记字段或表结构）

            $result = $order_common_model->getOrderPageList(
                $condition,
                1,
                $count,
                'pay_time asc',
                '*'
            );

            return json($result);
        }
    }

    /**
     * 一次生产：为今日分配一单（写入生产日期/序号/总量）
     * 过滤：待发货 + extend_id>0 + 未标记今日序号
     * 并发安全：事务 + 唯一索引(生产日期, 序号)
     */
    public function assignOneProductionOrder()
    {
        if (!request()->isAjax()) return;

        $inputTotal = (int) input('total', 0);
        $today = date('Y-m-d');

        try {
            \think\facade\Db::startTrans();

            // 计算当日 nextSeq 与已存在的 production_total
            $stats = \think\facade\Db::name('order')
                ->where([['site_id', '=', $this->site_id], ['production_date', '=', $today]])
                ->field('MAX(production_seq) as max_seq, MAX(production_total) as max_total, COUNT(*) as cnt')
                ->find();

            $nextSeq = (int)($stats['max_seq'] ?? 0) + 1;
            $existingTotal = (int)($stats['max_total'] ?? 0);

            // 解析本次应使用的 total：优先入参，否则沿用已存在，否则用 nextSeq 兜底
            $resolvedTotal = $inputTotal > 0 ? $inputTotal : ($existingTotal > 0 ? $existingTotal : $nextSeq);

            // 按规则选取一单（待发货 + extend_id>0 + 未标记今日）
            $candidate = \think\facade\Db::name('order')
                ->where([['site_id', '=', $this->site_id]])
                ->where([['is_delete', '=', 0]])
                ->where([['order_status', '=', OrderCommonModel::ORDER_PAY]])
                ->whereRaw("extend_id <> '0' AND extend_id <> ''")
                ->where(function($q) use ($today) {
                    $q->where([['production_date', '<>', $today]])
                      ->whereOr([['production_seq', '=', 0]]);
                })
                ->order('pay_time asc, create_time asc')
                ->lock(true)
                ->find();

            if (empty($candidate)) {
                \think\facade\Db::commit();
                return json(['code' => 0, 'message' => '今日无可分配订单', 'data' => ['order' => null, 'seq' => 0, 'total' => $resolvedTotal]]);
            }

            // 写入生产信息
            $update = [
                'production_date' => $today,
                'production_seq' => $nextSeq,
                'production_total' => $resolvedTotal,
            ];
            \think\facade\Db::name('order')->where([['order_id', '=', $candidate['order_id']]])->update($update);

            // 若本次传入 total 且与既有不同，同步更新当日所有已分配订单的 production_total，保持一致
            if ($inputTotal > 0 && $inputTotal != $existingTotal) {
                \think\facade\Db::name('order')
                    ->where([['site_id', '=', $this->site_id], ['production_date', '=', $today]])
                    ->update(['production_total' => $resolvedTotal]);
            }

            \think\facade\Db::commit();

            $candidate['production_seq'] = $nextSeq;
            $candidate['production_total'] = $resolvedTotal;
            $candidate['production_date'] = $today;

            return json(['code' => 0, 'message' => '分配成功', 'data' => [
                'order' => $candidate,
                'seq' => $nextSeq,
                'total' => $resolvedTotal
            ]]);
        } catch (\Throwable $e) {
            \think\facade\Db::rollback();
            return json(['code' => -1, 'message' => '分配失败：' . $e->getMessage()]);
        }
    }

    /**
     * 搜索生产订单
     * 支持按订单号搜索、前N条、全部等方式
     */
    public function searchProductionOrders()
    {
        if (!request()->isAjax()) return;

        $searchType = input('search_type', '');
        $orderNo = input('order_no', '');
        $limit = (int) input('limit', 20);

        try {
            $today = date('Y-m-d');
        
            // 基础过滤条件：待发货 + extend_id>0 + 未分配今日生产序号
            $query = \think\facade\Db::name('order')
                ->where([['site_id', '=', $this->site_id]])
                ->where([['is_delete', '=', 0]])
                ->where([['order_status', '=', OrderCommonModel::ORDER_PAY]])
                ->whereRaw("extend_id <> '0' AND extend_id <> ''")
                ->where(function($q) use ($today) {
                    // 只显示今日未分配生产序号的订单
                    $q->where([['production_date', '<>', $today]]) // 不是今日分配的
                      ->whereOr([['production_date', '=', null]]) // 或者未设置分配日期
                      ->whereOr([['production_seq', '=', null]]) // 或者未设置生产序号
                      ->whereOr([['production_seq', '=', 0]]); // 或者生产序号为0
                });

            // 根据搜索类型添加条件
            if ($searchType === 'order_no' && !empty($orderNo)) {
                $query->where('order_no', 'like', '%' . $orderNo . '%');
            }

            // 排序：按支付时间排序
            $query->order('pay_time asc, create_time asc');

            // 限制数量
            if ($limit > 0) {
                $query->limit($limit);
            }

            $orders = $query->select()->toArray();

            return json(['code' => 0, 'message' => '搜索成功', 'data' => ['list' => $orders]]);

        } catch (\Throwable $e) {
            return json(['code' => -1, 'message' => '搜索失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量分配生产订单
     * 为选中的多个订单分配生产序号
     */
    public function batchAssignProductionOrders()
    {
        if (!request()->isAjax()) return;

        $orderIds = input('order_ids', []);
        $inputTotal = (int) input('total', 0);
        $today = date('Y-m-d');

        if (empty($orderIds) || !is_array($orderIds)) {
            return json(['code' => -1, 'message' => '请选择要分配的订单']);
        }

        // 1. 检查散药库存是否充足
        $stockCheckResult = $this->checkOrdersLooseStockRequirement($orderIds);
        if ($stockCheckResult['code'] !== 0) {
            return json($stockCheckResult);
        }

        try {
            \think\facade\Db::startTrans();

            // 获取当前最大序号
            $stats = \think\facade\Db::name('order')
                ->where([['site_id', '=', $this->site_id], ['production_date', '=', $today]])
                ->field('MAX(production_seq) as max_seq, MAX(production_total) as max_total')
                ->find();

            $nextSeq = (int)($stats['max_seq'] ?? 0);
            $existingTotal = (int)($stats['max_total'] ?? 0);
            $resolvedTotal = $inputTotal > 0 ? $inputTotal : ($existingTotal > 0 ? $existingTotal : count($orderIds));

            $successCount = 0;
            $failedOrders = [];

            // 2. 扣减散药库存
            $stockDeductResult = $this->deductLooseStockForOrders($orderIds);
            if ($stockDeductResult['code'] !== 0) {
                \think\facade\Db::rollback();
                return json($stockDeductResult);
            }

            foreach ($orderIds as $orderId) {
                // 检查订单是否符合条件
                $order = \think\facade\Db::name('order')
                    ->where([
                        ['order_id', '=', $orderId],
                        ['site_id', '=', $this->site_id]
                    ])
                    ->lock(true)
                    ->find();

                if (empty($order)) {
                    $failedOrders[] = $orderId . '(订单不存在)';
                    continue;
                }

                // 详细检查失败原因
                if ($order['is_delete'] == 1) {
                    $failedOrders[] = $orderId . '(订单已删除)';
                    continue;
                }
                
                if ($order['order_status'] != OrderCommonModel::ORDER_PAY) {
                    $failedOrders[] = $orderId . '(订单状态不符合)';
                    continue;
                }
                
                if (empty($order['extend_id']) || $order['extend_id'] == '0') {
                    $failedOrders[] = $orderId . '(缺少外部订单号)';
                    continue;
                }

                // 如果已经分配了今日序号，跳过
                if ($order['production_date'] === $today && $order['production_seq'] > 0) {
                    continue;
                }

                // 分配序号
                $nextSeq++;
                $update = [
                    'production_date' => $today,
                    'production_seq' => $nextSeq,
                    'production_total' => $resolvedTotal,
                ];

                try {
                    $result = \think\facade\Db::name('order')
                        ->where([['order_id', '=', $orderId]])
                        ->update($update);

                    if ($result) {
                        $successCount++;
                    } else {
                        $failedOrders[] = $orderId . '(更新失败-无影响行数)';
                    }
                } catch (\Throwable $updateError) {
                    $failedOrders[] = $orderId . '(更新异常:' . $updateError->getMessage() . ')';
                }
            }

            // 如果设置了新的总量，更新当日所有订单的总量
            if ($inputTotal > 0 && $inputTotal != $existingTotal) {
                \think\facade\Db::name('order')
                    ->where([['site_id', '=', $this->site_id], ['production_date', '=', $today]])
                    ->update(['production_total' => $resolvedTotal]);
            }

            \think\facade\Db::commit();

            $message = "成功分配 {$successCount} 个订单";
            if (!empty($failedOrders)) {
                $failedCount = count($failedOrders);
                $message .= "，{$failedCount} 个订单分配失败";
            }

            return json([
                'code' => 0, 
                'message' => $message,
                'data' => [
                    'success_count' => $successCount,
                    'failed_orders' => $failedOrders,
                    'total' => $resolvedTotal
                ]
            ]);

        } catch (\Throwable $e) {
            \think\facade\Db::rollback();
            return json(['code' => -1, 'message' => '批量分配失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取生产信息（支持不同时间段）
     * 显示已分配生产序号的订单统计
     */
    public function getProductionInfo()
    {
        if (!request()->isAjax()) return;

        $period = input('period', 'today'); // today, yesterday, week
        $today = date('Y-m-d');
        
        // 根据时间段确定查询条件
        $dateCondition = [];
        $periodName = '';
        
        switch ($period) {
            case 'yesterday':
                $yesterday = date('Y-m-d', strtotime('-1 day'));
                $dateCondition = [['production_date', '=', $yesterday]];
                $periodName = '昨日';
                break;
            case 'week':
                $weekAgo = date('Y-m-d', strtotime('-6 days'));
                $dateCondition = [['production_date', '>=', $weekAgo], ['production_date', '<=', $today]];
                $periodName = '7天';
                break;
            case 'today':
            default:
                $dateCondition = [['production_date', '=', $today]];
                $periodName = '今日';
                break;
        }

        try {
            // 获取指定时间段已分配的订单统计
            $query = \think\facade\Db::name('order')
                ->where([['site_id', '=', $this->site_id]])
                ->where($dateCondition)
                ->where([['production_seq', '>', 0]]);

            $stats = $query->field('COUNT(*) as assigned_count, MAX(production_total) as total, MAX(production_seq) as max_seq')
                ->find();

            $assignedCount = (int)($stats['assigned_count'] ?? 0);
            $total = (int)($stats['total'] ?? 0);
            $maxSeq = (int)($stats['max_seq'] ?? 0);

            // 获取指定时间段已分配的订单列表
            $assignedOrders = \think\facade\Db::name('order')
                ->where([['site_id', '=', $this->site_id]])
                ->where($dateCondition)
                ->where([['production_seq', '>', 0]])
                ->field('order_id, order_no, production_seq, name, production_date, create_time')
                ->order('production_date desc, production_seq desc')
                ->limit(20)
                ->select()
                ->toArray();

            return json([
                'code' => 0,
                'message' => '获取成功',
                'data' => [
                    'period' => $period,
                    'period_name' => $periodName,
                    'assigned_count' => $assignedCount,
                    'total' => $total,
                    'max_seq' => $maxSeq,
                    'progress_percent' => $total > 0 ? round(($assignedCount / $total) * 100, 1) : 0,
                    'assigned_orders' => $assignedOrders
                ]
            ]);

        } catch (\Throwable $e) {
            return json(['code' => -1, 'message' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取今日生产信息（保持向后兼容）
     */
    public function getTodayProductionInfo()
    {
        // 重定向到新的通用方法
        $_POST['period'] = 'today';
        return $this->getProductionInfo();
    }

    /**
     * 筛选指定数量的订单
     * 替代原来的"分配一单"功能，可以筛选出指定数量的符合条件的订单
     */
    public function assignOrders()
    {
        if (!request()->isAjax()) return;

        $count = (int) input('count', 1);
        $today = date('Y-m-d');

        if ($count <= 0 || $count > 100) {
            return json(['code' => -1, 'message' => '请输入有效的订单数量（1-100）']);
        }

        try {
            // 获取符合条件的订单（待发货 + extend_id>0 + 未分配今日生产序号）
            $candidates = \think\facade\Db::name('order')
                ->where([['site_id', '=', $this->site_id]])
                ->where([['is_delete', '=', 0]])
                ->where([['order_status', '=', OrderCommonModel::ORDER_PAY]])
                ->whereRaw("extend_id <> '0' AND extend_id <> ''")
                ->where(function($q) use ($today) {
                // 只显示今日未分配生产序号的订单
                $q->where([['production_date', '<>', $today]]) // 不是今日分配的
                  ->whereOr([['production_date', '=', null]]) // 或者未设置分配日期
                  ->whereOr([['production_seq', '=', null]]) // 或者未设置生产序号
                  ->whereOr([['production_seq', '=', 0]]); // 或者生产序号为0
            })
                ->order('pay_time asc, create_time asc')
                ->limit($count)
                ->select()
                ->toArray();

            if (empty($candidates)) {
                return json(['code' => 0, 'message' => '没有找到符合条件的订单', 'data' => ['orders' => []]]);
            }

            return json([
                'code' => 0, 
                'message' => "成功筛选出 " . count($candidates) . " 个订单",
                'data' => [
                    'orders' => $candidates,
                    'count' => count($candidates)
                ]
            ]);

        } catch (\Throwable $e) {
            return json(['code' => -1, 'message' => '筛选失败：' . $e->getMessage()]);
        }
    }

    /**
     * 打印生产记录
     */
    public function printProduction()
    {
        $production_id = input('production_id', 0);

        if ($production_id <= 0) {
            $this->error('无效的生产记录ID');
        }

        try {
            // 获取生产记录详情
            $production_model = new PharmacyProductionLogModel();
            $result = $production_model->getProductionDetail($production_id);
            
            if ($result['code'] != 0) {
                $this->error($result['message'] ?: '获取生产记录失败');
            }

            $production_detail = $result['data'];
            
            // 获取模板信息以获取时间段数据
            $template_info = model('order_attr_class')->getInfo([['class_id', '=', $production_detail['template_id']]], 
                'class_id,class_name,sku_ids,goods_name,goods_num,sleep_time,earlymoning_time,moning_time,canjian_time,aftnoon_time,night_time');
            
            // 解析模板中的商品信息
            $template_sku_ids = [];
            $template_goods_names = [];
            $template_goods_nums = [];
            
            if ($template_info) {
                $template_sku_ids = array_filter(explode(',', $template_info['sku_ids']));
                $template_goods_names = json_decode($template_info['goods_name'], true) ?: [];
                $template_goods_nums = json_decode($template_info['goods_num'], true) ?: [];
            }
            
            // 创建SKU到时间段的映射
            $sku_time_mapping = [];
            foreach ($template_goods_names as $index => $goods_name_item) {
                $parts = explode('_', $goods_name_item);
                if (count($parts) >= 2) {
                    $time_period = $parts[0]; // 时间段标识
                    $sku_id = intval($parts[1]); // SKU ID
                    $sku_time_mapping[$sku_id] = $time_period;
                }
            }
            
            // 获取消耗的原料明细
            $consumed_materials = [];
            $material_summary = [
                'total_materials' => 0,
                'total_consumed' => 0,
                'average_cost' => 0,
                'max_consumed' => 0,
                'min_consumed' => 0
            ];
            
            if (!empty($production_detail['consumed_details'])) {
                $consumed_details = json_decode($production_detail['consumed_details'], true);
                if (is_array($consumed_details) && isset($consumed_details['items'])) {
                    $total_consumed = 0;
                    $max_consumed = 0;
                    $min_consumed = PHP_INT_MAX;
                    
                    foreach ($consumed_details['items'] as $detail) {
                        // 增加健壮性，防止缺少sku_id导致报错
                        if (!isset($detail['sku_id'])) {
                            continue; // 跳过此条目
                        }
                        
                        // 获取商品信息
                        $sku_info = model('goods_sku')->getInfo([['sku_id', '=', $detail['sku_id']]], 
                            'sku_id,sku_name,sku_no,sku_image,cost_price,daizhuang,shelf_no,goods_id');
                        
                        if ($sku_info) {
                            // 从数据库记录中获取消耗的颗数和瓶数
                            $consumed_grains = $detail['needed_grains'] ?? 0;  // 需要的颗数
                            $bottles_consumed = $detail['bottles_consumed'] ?? 0; // 消耗的瓶数
                            $unit_cost = floatval($detail['cost_price'] ?: $sku_info['cost_price'] ?: 0);
                            $subtotal = $consumed_grains * $unit_cost;
                            
                            // 获取商品的中文名和分类信息
                            $goods_info = model('goods')->getInfo([['goods_id', '=', $sku_info['goods_id'] ?: 0]], 'goods_chi,goods_class_name');
                            
                            // 获取时间段信息
                            $time_period = $sku_time_mapping[intval($detail['sku_id'])] ?? 'other';
                            
                            $consumed_materials[] = [
                                'sku_no' => $sku_info['sku_no'],
                                'shelf_no' => $sku_info['shelf_no'],
                                'category_name' => $goods_info['goods_class_name'] ?? '', // 使用goods表中的分类名称
                                'sku_name' => $detail['sku_name'] ?: $sku_info['sku_name'], // 优先使用记录中的名称
                                'goods_chi' => $goods_info['goods_chi'] ?? '', // 使用goods表中的中文名称
                                'sku_image' => $sku_info['sku_image'],
                                'consumed_quantity' => $consumed_grains, // 消耗的颗数
                                'bottles_consumed' => $bottles_consumed, // 消耗的瓶数
                                'grains_per_bottle' => $detail['grains_per_bottle'] ?? $sku_info['daizhuang'], // 每瓶颗数
                                'unit_cost' => $unit_cost,
                                'subtotal' => $subtotal,
                                'time_period' => $time_period // 添加时间段信息
                            ];
                            
                            $total_consumed += $consumed_grains;
                            $max_consumed = max($max_consumed, $consumed_grains);
                            $min_consumed = min($min_consumed, $consumed_grains);
                        }
                    }
                    
                    $material_count = count($consumed_materials);
                    if ($material_count > 0) {
                        $material_summary = [
                            'total_materials' => $material_count,
                            'total_consumed' => $total_consumed,
                            'average_cost' => $material_count > 0 ? ($production_detail['total_cost'] / $material_count) : 0,
                            'max_consumed' => $max_consumed,
                            'min_consumed' => $min_consumed == PHP_INT_MAX ? 0 : $min_consumed
                        ];
                    }
                }
            }
            
            // 计算包装材料统计
            $total_time_periods = 0;
            $time_periods = ['earlymoning_time', 'moning_time', 'canjian_time', 'aftnoon_time', 'night_time', 'sleep_time'];
            $active_periods = array(); // 用于调试，记录哪些时间段是活跃的
            $time_period_details = array(); // 记录每个时间段的详细内容

            foreach ($time_periods as $period) {
                $period_value = isset($template_info[$period]) ? $template_info[$period] : '';
                $time_period_details[$period] = $period_value;

                // 判断时间段是否有效：不为空且不为空字符串
                if (!empty($period_value) && trim($period_value) !== '') {
                    $total_time_periods++;
                    $active_periods[] = $period; // 记录活跃的时间段
                }
            }

            // 重要：当所有时间段都为空时，默认为1个时间段，以保证计算正确
            if ($total_time_periods == 0) {
                $total_time_periods = 1;
                $active_periods[] = 'default'; // 标记为默认时间段
            }

            // 计算各种包装材料数量（基于生产套数）
            $final_sets = (isset($production_detail['final_quantity']) && $production_detail['final_quantity'] > 0)
                ? $production_detail['final_quantity']
                : $production_detail['production_quantity'];

            // 根据用户需求修正计算逻辑：
            // 1. 餐盒、封口贴、封套：数量 = 套数 × 时间段数量
            // 2. 个性化说明书、外盒小：数量 = 套数（不乘以时间段数量）
            $packaging_counts = [
                // 按时间段数量×套数计算的材料
                'meal_box' => $total_time_periods * $final_sets,        // 餐盒数量 = 时间段数量 × 套数
                'envelope' => $total_time_periods * $final_sets,        // 封套数量 = 时间段数量 × 套数
                'seal_sticker' => $total_time_periods * $final_sets,    // 封口贴数量 = 时间段数量 × 套数

                // 按套数计算的材料（不乘以时间段数量）
                'brand_manual' => $final_sets,          // 品牌手册 = 套数
                'personal_manual' => $final_sets,       // 个性化说明书 = 套数
                'outer_box_small' => $final_sets,       // 外盒小 = 套数

                // 固定为0的材料
                'express_bill' => 0,                    // 快递单 = 0
                'outer_box_large' => 0                  // 外盒大 = 0
            ];



            // 生成二维码（可选）
            $production_qrcode_path = '';
            $template_qrcode_path = '';

            // 这里可以添加二维码生成逻辑
            // 例如使用 QR Code 库生成生产记录和模板的二维码

            // 赋值给模板
            $this->assign('production_detail', $production_detail);
            $this->assign('consumed_materials', $consumed_materials);
            $this->assign('material_summary', $material_summary);
            $this->assign('packaging_counts', $packaging_counts);
            $this->assign('total_time_periods', $total_time_periods);
            $this->assign('production_qrcode_path', $production_qrcode_path);
            $this->assign('template_qrcode_path', $template_qrcode_path);

            return $this->fetch('pharmacy/print_production');
            
        } catch (\Exception $e) {
            $this->error('获取生产记录失败：' . $e->getMessage());
        }
    }

    /**
     * 检查订单的散药库存需求
     * @param array $orderIds 订单ID数组
     * @return array
     */
    private function checkOrdersLooseStockRequirement($orderIds)
    {
        try {
            // 1. 获取所有订单的商品需求
            $orderGoodsQuery = \think\facade\Db::name('order_goods')
                ->alias('og')
                ->join('goods_sku gs', 'og.sku_id = gs.sku_id')
                ->where('og.order_id', 'in', $orderIds)
                ->field('og.sku_id, og.num, gs.sku_name, gs.loose_stock, gs.shelf_no, gs.daizhuang')
                ->select();

            if (empty($orderGoodsQuery)) {
                return ['code' => -1, 'message' => '订单中没有找到商品信息'];
            }

            // 2. 按SKU统计总需求量
            $skuRequirements = [];
            foreach ($orderGoodsQuery as $item) {
                $skuId = $item['sku_id'];
                if (!isset($skuRequirements[$skuId])) {
                    $skuRequirements[$skuId] = [
                        'sku_id' => $skuId,
                        'sku_name' => $item['sku_name'],
                        'required' => 0,
                        'available' => $item['loose_stock'],
                        'shelf_no' => $item['shelf_no'],
                        'daizhuang' => $item['daizhuang']
                    ];
                }
                $skuRequirements[$skuId]['required'] += $item['num'];
            }

            // 3. 检查库存是否充足
            $insufficientItems = [];
            foreach ($skuRequirements as $requirement) {
                if ($requirement['required'] > $requirement['available']) {
                    $insufficientItems[] = [
                        'sku_id' => $requirement['sku_id'],
                        'sku_name' => $requirement['sku_name'],
                        'required' => $requirement['required'],
                        'available' => $requirement['available'],
                        'shortage' => $requirement['required'] - $requirement['available'],
                        'shelf_no' => $requirement['shelf_no'],
                        'bottles_needed' => ceil(($requirement['required'] - $requirement['available']) / $requirement['daizhuang'])
                    ];
                }
            }

            // 4. 返回检查结果
            if (!empty($insufficientItems)) {
                return [
                    'code' => 1,
                    'message' => '散药库存不足，需要拆零操作',
                    'data' => [
                        'insufficient_items' => $insufficientItems
                    ]
                ];
            }

            return ['code' => 0, 'message' => '散药库存充足'];

        } catch (\Throwable $e) {
            return ['code' => -1, 'message' => '检查散药库存失败：' . $e->getMessage()];
        }
    }

    /**
     * 为订单扣减散药库存
     * @param array $orderIds 订单ID数组
     * @return array
     */
    private function deductLooseStockForOrders($orderIds)
    {
        try {
            // 1. 获取所有订单的商品需求
            $orderGoodsQuery = \think\facade\Db::name('order_goods')
                ->alias('og')
                ->join('goods_sku gs', 'og.sku_id = gs.sku_id')
                ->where('og.order_id', 'in', $orderIds)
                ->field('og.sku_id, og.num, gs.sku_name, gs.loose_stock')
                ->select();

            if (empty($orderGoodsQuery)) {
                return ['code' => -1, 'message' => '订单中没有找到商品信息'];
            }

            // 2. 按SKU统计总需求量
            $skuRequirements = [];
            foreach ($orderGoodsQuery as $item) {
                $skuId = $item['sku_id'];
                if (!isset($skuRequirements[$skuId])) {
                    $skuRequirements[$skuId] = [
                        'sku_id' => $skuId,
                        'sku_name' => $item['sku_name'],
                        'required' => 0,
                        'available' => $item['loose_stock']
                    ];
                }
                $skuRequirements[$skuId]['required'] += $item['num'];
            }

            // 3. 执行库存扣减
            $deductedItems = [];
            foreach ($skuRequirements as $requirement) {
                $skuId = $requirement['sku_id'];
                $deductAmount = $requirement['required'];

                // 再次检查库存（防止并发问题）
                $currentStock = \think\facade\Db::name('goods_sku')
                    ->where('sku_id', $skuId)
                    ->value('loose_stock');

                if ($currentStock < $deductAmount) {
                    return [
                        'code' => -1,
                        'message' => "商品 {$requirement['sku_name']} 散药库存不足，当前库存：{$currentStock}，需要：{$deductAmount}"
                    ];
                }

                // 扣减散药库存
                $result = \think\facade\Db::name('goods_sku')
                    ->where('sku_id', $skuId)
                    ->where('loose_stock', '>=', $deductAmount)
                    ->setDec('loose_stock', $deductAmount);

                if (!$result) {
                    return [
                        'code' => -1,
                        'message' => "扣减商品 {$requirement['sku_name']} 散药库存失败"
                    ];
                }

                $deductedItems[] = [
                    'sku_id' => $skuId,
                    'sku_name' => $requirement['sku_name'],
                    'deducted_amount' => $deductAmount
                ];

                // 记录库存操作日志
                $this->recordLooseStockDeductLog($skuId, $requirement['sku_name'], $deductAmount, $currentStock);
            }

            return [
                'code' => 0,
                'message' => '散药库存扣减成功',
                'data' => [
                    'deducted_items' => $deductedItems
                ]
            ];

        } catch (\Throwable $e) {
            return ['code' => -1, 'message' => '扣减散药库存失败：' . $e->getMessage()];
        }
    }

    /**
     * 记录散药库存扣减日志
     * @param int $skuId SKU ID
     * @param string $skuName SKU名称
     * @param int $deductAmount 扣减数量
     * @param int $beforeStock 扣减前库存
     */
    private function recordLooseStockDeductLog($skuId, $skuName, $deductAmount, $beforeStock)
    {
        try {
            $logData = [
                'site_id' => $this->site_id,
                'sku_id' => $skuId,
                'sku_name' => $skuName,
                'operation_type' => 3, // 生产扣减
                'operator_id' => $this->uid,
                'operator_name' => $this->user['user_name'] ?? '系统',
                'before_whole_stock' => 0, // 不涉及整瓶库存
                'before_loose_stock' => $beforeStock,
                'after_whole_stock' => 0,
                'after_loose_stock' => $beforeStock - $deductAmount,
                'change_amount' => -$deductAmount,
                'remark' => "生产任务扣减散药库存：-{$deductAmount}颗",
                'create_time' => date('Y-m-d H:i:s')
            ];

            \think\facade\Db::name('pharmacy_stock_log')->insert($logData);
        } catch (\Throwable $e) {
            // 日志记录失败不影响主流程，只记录错误
            \think\facade\Log::error('记录散药库存扣减日志失败：' . $e->getMessage());
        }
    }
}
